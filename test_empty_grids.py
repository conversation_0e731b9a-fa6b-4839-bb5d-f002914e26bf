#!/usr/bin/env python3
"""
Test script to check for empty grid issues
"""

import os
import sys
import numpy as np

# Add the project root to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

from pointcept.datasets.powscan import PowScanDataset

def test_empty_grids():
    """Test if the current implementation creates empty grids"""
    print("Testing for empty grids with current implementation...")
    
    # Test with a real file
    test_file = "data/powscan/processed/数据9/17-18(17_18).npy"
    if not os.path.exists(test_file):
        print(f"Test file {test_file} not found")
        return
    
    # Load the data
    data = np.load(test_file)
    print(f"Loaded file: {test_file}")
    print(f"Data shape: {data.shape}")
    
    # Create dataset and data dict
    dataset = PowScanDataset(split="train", data_root="data/powscan")
    
    data_dict = {
        "coord": data[:, :3].astype(np.float32),
        "strength": data[:, 3:4].astype(np.float32),
        "color": data[:, 4:7].astype(np.float32),
        "segment": data[:, 7].astype(np.int32),
        "name": os.path.basename(test_file)
    }
    
    # Test grid splitting
    print(f"\nTesting grid splitting with {data_dict['coord'].shape[0]} points...")
    segments = dataset._split_by_coordinate_grid(data_dict)
    print(f"Created {len(segments)} segments")
    
    empty_segments = 0
    small_segments = 0
    
    for i, segment in enumerate(segments):
        point_count = segment["coord"].shape[0]
        if point_count == 0:
            empty_segments += 1
            print(f"  Segment {i}: EMPTY!")
        elif point_count < 1000:  # Very small segments
            small_segments += 1
            print(f"  Segment {i}: {point_count} points (very small)")
        else:
            print(f"  Segment {i}: {point_count} points")
    
    print(f"\nSummary:")
    print(f"  Total segments: {len(segments)}")
    print(f"  Empty segments: {empty_segments}")
    print(f"  Small segments (<1000 points): {small_segments}")
    
    if empty_segments > 0:
        print(f"  WARNING: Found {empty_segments} empty segments!")
        return False
    
    return True

def test_grid_boundaries():
    """Test grid boundary calculation for edge cases"""
    print("\nTesting grid boundary calculations...")
    
    dataset = PowScanDataset(split="train", data_root="data/powscan")
    
    # Test case: very small spatial extent
    min_coord = np.array([0.0, 0.0, 0.0])
    spatial_extent = np.array([0.1, 0.1, 0.1])  # Very small extent
    grid_dims = [1, 2, 1]
    
    print(f"Testing with small spatial extent: {spatial_extent}")
    print(f"Grid dimensions: {grid_dims}")
    
    for i in range(grid_dims[0]):
        for j in range(grid_dims[1]):
            for k in range(grid_dims[2]):
                boundaries = dataset._get_grid_boundaries(min_coord, spatial_extent, grid_dims, i, j, k)
                print(f"  Grid [{i},{j},{k}]: x=[{boundaries['start_x']:.3f}, {boundaries['end_x']:.3f}], "
                      f"y=[{boundaries['start_y']:.3f}, {boundaries['end_y']:.3f}], "
                      f"z=[{boundaries['start_z']:.3f}, {boundaries['end_z']:.3f}]")
    
    # Test case: zero spatial extent in one dimension
    spatial_extent_flat = np.array([10.0, 0.0, 5.0])  # Zero Y extent
    print(f"\nTesting with zero Y extent: {spatial_extent_flat}")
    
    try:
        boundaries = dataset._get_grid_boundaries(min_coord, spatial_extent_flat, [1, 1, 1], 0, 0, 0)
        print(f"  Boundaries: x=[{boundaries['start_x']:.3f}, {boundaries['end_x']:.3f}], "
              f"y=[{boundaries['start_y']:.3f}, {boundaries['end_y']:.3f}], "
              f"z=[{boundaries['start_z']:.3f}, {boundaries['end_z']:.3f}]")
    except Exception as e:
        print(f"  Error: {e}")

def test_min_points_threshold():
    """Test the minimum points threshold logic"""
    print("\nTesting minimum points threshold...")
    
    # Create a simple test case
    coords = np.array([
        [0, 0, 0],
        [1, 0, 0], 
        [0, 1, 0],
        [1, 1, 0],
        [0, 0, 1],
        [1, 0, 1],
        [0, 1, 1],
        [1, 1, 1]
    ], dtype=np.float32)
    
    data_dict = {
        "coord": coords,
        "strength": np.ones((8, 1), dtype=np.float32),
        "segment": np.zeros(8, dtype=np.int32),
        "name": "test"
    }
    
    dataset = PowScanDataset(split="train", data_root="data/powscan")
    
    # Test with high minimum points threshold
    print(f"Testing with {len(coords)} points and min_points=120000...")
    segments = dataset._split_by_coordinate_grid(data_dict, min_points=120000)
    print(f"Created {len(segments)} segments")
    
    for i, segment in enumerate(segments):
        print(f"  Segment {i}: {segment['coord'].shape[0]} points")

if __name__ == "__main__":
    success = test_empty_grids()
    test_grid_boundaries()
    test_min_points_threshold()
    
    if not success:
        print("\nFound issues that could cause training failures!")
    else:
        print("\nNo empty grid issues found with current data.")
