"""
Preprocessing Script for PowScan Dataset

Author: Your Name
Please cite our work if the code is helpful to you.
"""

import os
import sys
import argparse
import glob
import json
import numpy as np
import laspy
from scipy.spatial import cKDTree
from concurrent.futures import ProcessPoolExecutor
from itertools import repeat
from collections import defaultdict

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 定义常量
UNLABELED_ID = 0


def parse_scene(scene_path, output_root, predefined_class_mapping=None):
    """
    解析单个场景
    
    Args:
        scene_path: 场景路径
        output_root: 输出根目录
        predefined_class_mapping: 预定义的类别映射
    
    Returns:
        dict: 类别映射字典
    """
    print(f"Processing scene: {scene_path}")
    
    # 如果没有预定义映射，则尝试从配置文件加载默认映射
    if predefined_class_mapping is None:
        try:
            from configs._base_.dataset.powscan import CLASS2ID as predefined_class_mapping
        except ImportError:
            predefined_class_mapping = {}
    
    # 获取场景名称
    scene_name = os.path.basename(scene_path)
    output_scene_dir = os.path.join(output_root, scene_name)
    os.makedirs(output_scene_dir, exist_ok=True)
    
    # 查找原始切档目录和类别目录
    raw_dir = None
    class_dirs = []
    
    for item in os.listdir(scene_path):
        item_path = os.path.join(scene_path, item)
        if os.path.isdir(item_path):
            # 检查是否为原始切档目录（支持多种命名）
            if item in ["原始切档目录", "原始切档数据"]:
                raw_dir = item_path
            elif item.startswith("类别") and "-" in item:
                class_dirs.append(item_path)
    
    if raw_dir is None:
        print(f"Warning: No raw directory found in {scene_path}")
        return {}
    
    # 获取所有原始LAS文件
    raw_files = glob.glob(os.path.join(raw_dir, "*.las"))
    if not raw_files:
        print(f"Warning: No LAS files found in {raw_dir}")
        return {}
    
    # 初始化类别映射（仅在没有预定义映射时使用）
    if not predefined_class_mapping:
        class_mapping = {}
    else:
        class_mapping = predefined_class_mapping
    
    # 处理每个原始文件
    for raw_file in raw_files:
        process_fragment(raw_file, class_dirs, output_scene_dir, predefined_class_mapping)
    
    # 如果没有预定义映射，返回当前场景的映射用于合并
    if not predefined_class_mapping:
        return class_mapping
    else:
        return predefined_class_mapping


def process_fragment(raw_file, class_dirs, output_scene_dir, predefined_class_mapping=None):
    """
    处理单个片段文件
    
    Args:
        raw_file: 原始LAS文件路径
        class_dirs: 类别目录列表
        output_scene_dir: 输出场景目录
        predefined_class_mapping: 预定义的类别映射字典
    """
    # 如果没有预定义映射，则尝试从配置文件加载默认映射
    if predefined_class_mapping is None:
        try:
            from configs._base_.dataset.powscan import CLASS2ID as predefined_class_mapping
        except ImportError:
            predefined_class_mapping = {}
    fragment_name = os.path.splitext(os.path.basename(raw_file))[0]
    print(f"  Processing fragment: {fragment_name}")
    
    # 读取原始点云
    try:
        with laspy.open(raw_file) as las_file:
            las = las_file.read()
            raw_coords = np.vstack((las.x, las.y, las.z)).T
            
            # 提取并处理强度值
            if hasattr(las, 'intensity'):
                # LAS强度值通常为16位无符号整数，归一化到[0,1]
                raw_intensity = las.intensity.astype(np.float32).reshape(-1, 1) / 65535.0
            else:
                raw_intensity = np.zeros((len(raw_coords), 1), dtype=np.float32)
            
            # 检查是否有颜色信息
            if hasattr(las, 'red') and hasattr(las, 'green') and hasattr(las, 'blue'):
                # 归一化颜色值到[0, 1]范围
                raw_colors = np.vstack((las.red, las.green, las.blue)).T.astype(np.float32) / 65535.0
            else:
                # 如果没有颜色信息，创建默认颜色（灰色）
                raw_colors = np.full((len(raw_coords), 3), 0.5, dtype=np.float32)
    except Exception as e:
        print(f"  Error reading {raw_file}: {e}")
        return
    
    # 初始化标签数组
    labels = np.full(len(raw_coords), UNLABELED_ID, dtype=np.int32)
    
    # 处理每个类别目录
    for class_dir in class_dirs:
        # 解析类别信息 (格式: 类别X-名称)
        dir_name = os.path.basename(class_dir)
        parts = dir_name.split("-", 1)
        if len(parts) != 2:
            print(f"  Warning: Invalid class directory name: {dir_name}")
            continue
            
        class_id_str, class_name = parts
        try:
            class_id = int(class_id_str.replace("类别", ""))
        except ValueError:
            print(f"  Warning: Invalid class ID in directory name: {dir_name}")
            continue
        
        # 获取全局类别ID
        if predefined_class_mapping and class_name in predefined_class_mapping:
            global_class_id = predefined_class_mapping[class_name]
        else:
            # 未定义的类别映射为背景类0
            global_class_id = 0
            print(f"  Warning: Undefined class '{class_name}' mapped to background class (ID: 0)")
        
        # 查找对应的分类文件
        # 尝试多种可能的文件名格式
        possible_filenames = [
            f"{fragment_name}{class_name}.las",
            f"{fragment_name}({class_name}).las",
            f"{fragment_name}_{class_name}.las"
        ]
        
        class_file_path = None
        for filename in possible_filenames:
            path = os.path.join(class_dir, filename)
            if os.path.exists(path):
                class_file_path = path
                break
        
        # 如果上述格式都不匹配，尝试通配符匹配
        if class_file_path is None:
            class_files = glob.glob(os.path.join(class_dir, f"{fragment_name}*{class_name}*.las"))
            if class_files:
                class_file_path = class_files[0]
        
        if class_file_path is None:
            print(f"  Warning: No class file found for {fragment_name} in {class_dir}")
            continue
        
        # 读取分类点云
        try:
            with laspy.open(class_file_path) as las_file:
                class_las = las_file.read()
                class_coords = np.vstack((class_las.x, class_las.y, class_las.z)).T
        except Exception as e:
            print(f"  Error reading {class_file_path}: {e}")
            continue
        
        # 使用KDTree进行坐标匹配
        if len(class_coords) > 0:
            try:
                tree = cKDTree(raw_coords)
                _, indices = tree.query(class_coords)
                
                # 为匹配的点分配标签
                labels[indices] = global_class_id
            except Exception as e:
                print(f"  Error during KDTree matching: {e}")
                continue
    
    # 保存处理后的数据
    output_file = os.path.join(output_scene_dir, fragment_name + ".npy")
    # 组合坐标、强度、颜色和标签数据
    data = np.hstack((raw_coords, raw_intensity, raw_colors, labels.reshape(-1, 1)))
    np.save(output_file, data.astype(np.float32))
    print(f"  Saved processed fragment to {output_file}")


def merge_class_mappings(mappings):
    """
    合并多个类别映射
    
    Args:
        mappings: 类别映射字典列表
    
    Returns:
        dict: 合并后的类别映射
    """
    merged_mapping = {}
    for mapping in mappings:
        for class_name, class_id in mapping.items():
            if class_name not in merged_mapping:
                merged_mapping[class_name] = class_id
    return merged_mapping


def main_process():
    parser = argparse.ArgumentParser(description="Preprocess PowScan dataset")
    parser.add_argument(
        "--data_root", 
        required=True, 
        help="Path to PowScan dataset root directory"
    )
    parser.add_argument(
        "--output_root", 
        default=None, 
        help="Output path for processed data (default: data_root/processed)"
    )
    parser.add_argument(
        "--num_workers", 
        default=1, 
        type=int, 
        help="Number of workers for parallel processing"
    )
    args = parser.parse_args()
    
    # 设置输出目录
    if args.output_root is None:
        args.output_root = os.path.join(args.data_root, "processed")
    os.makedirs(args.output_root, exist_ok=True)
    
    # 加载预定义的类别映射
    predefined_mapping_path = os.path.join(args.output_root, "class_mapping.json")
    if os.path.exists(predefined_mapping_path):
        with open(predefined_mapping_path, 'r', encoding='utf-8') as f:
            predefined_class_mapping = json.load(f)
        print(f"Loaded predefined class mapping from {predefined_mapping_path}")
    else:
        # 如果没有预定义映射，则尝试从配置文件加载默认映射
        try:
            from configs._base_.dataset.powscan import CLASS2ID as predefined_class_mapping
            print("Loaded default class mapping from config file")
        except ImportError:
            predefined_class_mapping = None
            print(f"No predefined class mapping found at {predefined_mapping_path}")
    
    # 获取所有场景目录
    scene_dirs = []
    for item in os.listdir(args.data_root):
        item_path = os.path.join(args.data_root, item)
        if os.path.isdir(item_path) and item.startswith("数据"):
            scene_dirs.append(item_path)
    
    print(f"Found {len(scene_dirs)} scenes to process")
    
    # 处理所有场景
    all_class_mappings = []
    if args.num_workers > 1:
        print(f"Processing with {args.num_workers} workers")
        with ProcessPoolExecutor(max_workers=args.num_workers) as executor:
            futures = [executor.submit(parse_scene, scene_dir, args.output_root, predefined_class_mapping) for scene_dir in scene_dirs]
            for future in futures:
                result = future.result()
                if not predefined_class_mapping and result:
                    all_class_mappings.append(result)  # 收集映射用于合并
    else:
        print("Processing sequentially")
        for scene_dir in scene_dirs:
            result = parse_scene(scene_dir, args.output_root, predefined_class_mapping)
            if not predefined_class_mapping and result:
                all_class_mappings.append(result)
    
    # 如果没有预定义映射，则合并所有场景的映射并保存
    if not predefined_class_mapping and all_class_mappings:
        final_class_mapping = merge_class_mappings(all_class_mappings)
        with open(predefined_mapping_path, 'w', encoding='utf-8') as f:
            json.dump(final_class_mapping, f, ensure_ascii=False, indent=2)
        print(f"Created class mapping file at {predefined_mapping_path}")
    elif not predefined_class_mapping and not all_class_mappings:
        # 如果既没有预定义映射也没有场景映射，则保存默认映射
        try:
            from configs._base_.dataset.powscan import CLASS2ID as default_mapping
            with open(predefined_mapping_path, 'w', encoding='utf-8') as f:
                json.dump(default_mapping, f, ensure_ascii=False, indent=2)
            print(f"Created class mapping file from default config at {predefined_mapping_path}")
        except ImportError:
            print("No default class mapping available to save")
    
    print("Preprocessing completed!")


if __name__ == "__main__":
    main_process()