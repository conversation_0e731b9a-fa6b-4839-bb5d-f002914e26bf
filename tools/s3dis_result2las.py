import numpy as np
import argparse
import logging
import os
import sys
import laspy

def validate_point_cloud(xyz, labels=None):
    """验证点云数据有效性"""
    if xyz.size == 0:
        raise ValueError("输入点云数据为空")
    if xyz.shape[1] != 3:
        raise ValueError(f"点云维度应为3维，当前维度为{xyz.shape[1]}")
    if labels is not None:
        if len(labels.shape) != 1:
            raise ValueError(f"分类号应为1维数组，当前维度为{labels.shape}")
        if xyz.shape[0] != labels.shape[0]:
            raise ValueError(f"坐标数据点数({xyz.shape[0]})与分类号数量({labels.shape[0]})不一致")

def get_colormap(cmap_path=None):
    """获取颜色映射表"""
    if cmap_path:
        colormap = np.load(cmap_path)
        if colormap.shape[1] != 3 or colormap.min() < 0 or colormap.max() > 1:
            raise ValueError("颜色映射表应为Nx3数组，且值在0-1范围内")
        return colormap
    # 默认使用tab10调色板
    return np.array([
        [0.1216, 0.4667, 0.7055],  # blue
        [1.0000, 0.4980, 0.0549],  # orange
        [0.1725, 0.6275, 0.1725],  # green
        [0.8392, 0.1529, 0.1569],  # red
        [0.5804, 0.4039, 0.7412],  # purple
        [0.5490, 0.3373, 0.2941],  # brown
        [0.8902, 0.4667, 0.7608],  # pink
        [0.4980, 0.4980, 0.4980],  # gray
        [0.7373, 0.7412, 0.1333],  # lime
        [0.0902, 0.7451, 0.8118]   # cyan
    ])

def main():
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

    # 解析命令行参数
    parser = argparse.ArgumentParser(description='转换numpy点云数据为LAS格式')
    parser.add_argument('-i', '--input', required=True, help='输入坐标numpy文件路径')
    parser.add_argument('-c', '--color', help='分类号numpy文件路径')
    parser.add_argument('-o', '--output', required=True, help='输出LAS文件路径')
    parser.add_argument('--colormap', help='自定义颜色映射numpy文件路径（Nx3 RGB数组）')
    args = parser.parse_args()

    try:
        # 检查输入文件是否存在
        if not os.path.exists(args.input):
            raise FileNotFoundError(f"输入文件不存在: {args.input}")

        logging.info(f"开始处理文件: {args.input}")
        
        # 加载数据并验证
        logging.info(f"加载坐标文件: {args.input}")
        xyz = np.load(args.input)
        
        labels = None
        colors = np.zeros((len(xyz), 3), dtype=np.uint16)
        if args.color:
            logging.info(f"加载分类号文件: {args.color}")
            labels = np.load(args.color)
            validate_point_cloud(xyz, labels)
            
            # 加载颜色映射表
            colormap = get_colormap(args.colormap)
            logging.info(f"使用颜色映射表包含{len(colormap)}种颜色")
            
            # 分配颜色（处理超出颜色表范围的分类号）
            normalized_labels = labels % len(colormap)
            colors_float = colormap[normalized_labels]
            colors = (np.clip(colors_float, 0.0, 1.0) * 65535).astype(np.uint16)

        validate_point_cloud(xyz)

        # 创建LAS文件
        logging.info("创建LAS文件...")
        header = laspy.LasHeader(version="1.4", point_format=7)
        header.offsets = np.min(xyz, axis=0)
        header.scales = [0.001, 0.001, 0.001]  # 毫米精度

        las = laspy.LasData(header)
        las.x = xyz[:, 0]
        las.y = xyz[:, 1]
        las.z = xyz[:, 2]
        
        if labels is not None:
            las.classification = labels.astype(np.uint8)
            las.red = colors[:, 0]
            las.green = colors[:, 1]
            las.blue = colors[:, 2]

        # 创建输出目录
        os.makedirs(os.path.dirname(args.output), exist_ok=True)

        # 保存LAS文件
        logging.info(f"保存结果到: {args.output}")
        las.write(args.output)
        
        logging.info("转换完成")

    except Exception as e:
        logging.error(f"处理失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()