#!/usr/bin/env python3
"""
SemanticKITTI颜色映射表
参考官方SemanticKITTI数据集文档和常见实现
"""

import numpy as np

# SemanticKITTI的19个语义类别标准颜色映射
# 颜色值范围为0-255
SEMANTIC_KITTI_COLOR_MAP = {
    0: [245, 150, 100],    # car
    1: [245, 230, 100],    # bicycle
    2: [150, 60, 30],      # motorcycle
    3: [180, 30, 80],      # truck
    4: [255, 0, 0],        # other-vehicle
    5: [30, 30, 255],      # person
    6: [200, 40, 255],     # bicyclist
    7: [90, 30, 150],      # motorcyclist
    8: [255, 0, 255],      # road
    9: [255, 150, 255],    # parking
    10: [75, 0, 75],       # sidewalk
    11: [75, 0, 175],      # other-ground
    12: [0, 200, 255],     # building
    13: [50, 120, 255],    # fence
    14: [0, 175, 0],       # vegetation
    15: [0, 60, 135],      # trunk
    16: [80, 240, 150],    # terrain
    17: [150, 240, 255],   # pole
    18: [0, 0, 255],       # traffic-sign
}

# 忽略标签的颜色（黑色）
IGNORE_COLOR = [0, 0, 0]

def get_semantic_kitti_colors(labels, ignore_index=-1):
    """
    将语义标签转换为RGB颜色值（向量化版本）
    
    Args:
        labels: numpy数组，语义标签
        ignore_index: 忽略的标签索引
        
    Returns:
        numpy数组，RGB颜色值 (N, 3)
    """
    # 初始化颜色数组
    colors = np.zeros((len(labels), 3), dtype=np.uint8)
    
    # 为忽略标签分配黑色
    ignore_mask = (labels == ignore_index)
    colors[ignore_mask] = IGNORE_COLOR
    
    # 为每个有效标签分配颜色
    for label, color in SEMANTIC_KITTI_COLOR_MAP.items():
        mask = (labels == label)
        colors[mask] = color
    
    # 为不在映射表中的标签分配默认颜色（灰色）
    valid_labels = set(SEMANTIC_KITTI_COLOR_MAP.keys())
    unknown_mask = ~ignore_mask & ~np.isin(labels, list(valid_labels))
    colors[unknown_mask] = [128, 128, 128]
    
    return colors

def get_semantic_kitti_colormap():
    """
    获取SemanticKITTI颜色映射表
    
    Returns:
        numpy数组，颜色映射表 (19, 3)
    """
    colormap = np.zeros((19, 3), dtype=np.uint8)
    for i in range(19):
        if i in SEMANTIC_KITTI_COLOR_MAP:
            colormap[i] = SEMANTIC_KITTI_COLOR_MAP[i]
        else:
            colormap[i] = IGNORE_COLOR
    return colormap

if __name__ == "__main__":
    # 测试颜色映射
    test_labels = np.array([0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18])
    colors = get_semantic_kitti_colors(test_labels)
    print("SemanticKITTI颜色映射表:")
    for i, (label, color) in enumerate(zip(test_labels, colors)):
        print(f"  标签 {label}: RGB{tuple(color)}")