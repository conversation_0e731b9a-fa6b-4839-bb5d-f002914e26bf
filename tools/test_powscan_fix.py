#!/usr/bin/env python3
"""
Test script for PowScan dataset fix verification

This script verifies that the fixes for handling empty arrays in the PowScan dataset
are working correctly.
"""

import os
import sys
import numpy as np
import tempfile
import shutil

# Add the project root to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from pointcept.datasets.powscan import PowScanDataset


def create_test_data(data_dir, filename, num_points):
    """Create test data with specified number of points"""
    # Create a sample point cloud
    coords = np.random.rand(num_points, 3) * 100  # Random coordinates in 100x100x100 space
    strength = np.random.rand(num_points, 1) * 255  # Random strength values
    segment = np.random.randint(0, 10, (num_points, 1))  # Random segment labels
    
    # Combine into a single array (5 columns: xyz + strength + label)
    data = np.hstack([coords, strength, segment])
    
    # Save to file
    filepath = os.path.join(data_dir, filename)
    np.save(filepath, data)
    
    return filepath


def test_empty_grid_handling():
    """Test that empty grids are handled correctly"""
    print("Testing empty grid handling...")
    
    # Create a temporary directory for test data
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create processed directory
        processed_dir = os.path.join(temp_dir, "processed")
        os.makedirs(processed_dir)
        
        # Create a scene directory
        scene_dir = os.path.join(processed_dir, "test_scene")
        os.makedirs(scene_dir)
        
        # Create a large point cloud file (more than 120,000 points)
        large_file = create_test_data(scene_dir, "large_cloud.npy", 150000)
        
        # Create train.txt file
        train_file = os.path.join(temp_dir, "train.txt")
        with open(train_file, 'w') as f:
            f.write(os.path.join(scene_dir, "large_cloud"))
        
        # Initialize the dataset
        dataset = PowScanDataset(
            split="train",
            data_root=temp_dir,
            test_mode=False
        )
        
        # Check that we have data samples
        print(f"Dataset size: {len(dataset.data_list)}")
        
        # Try to load a few samples
        for i in range(min(5, len(dataset))):
            try:
                data = dataset.get_data(i)
                print(f"Sample {i}: coord shape = {data['coord'].shape}")
                assert data['coord'].shape[0] > 0, "Coordinate array should not be empty"
            except Exception as e:
                print(f"Error loading sample {i}: {e}")
                return False
        
        print("Empty grid handling test passed!")
        return True


def test_edge_cases():
    """Test edge cases that might cause empty arrays"""
    print("Testing edge cases...")
    
    # Create a temporary directory for test data
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create processed directory
        processed_dir = os.path.join(temp_dir, "processed")
        os.makedirs(processed_dir)
        
        # Create a scene directory
        scene_dir = os.path.join(processed_dir, "test_scene")
        os.makedirs(scene_dir)
        
        # Create a small point cloud file (less than 120,000 points)
        small_file = create_test_data(scene_dir, "small_cloud.npy", 50000)
        
        # Create train.txt file
        train_file = os.path.join(temp_dir, "train.txt")
        with open(train_file, 'w') as f:
            f.write(os.path.join(scene_dir, "small_cloud"))
        
        # Initialize the dataset
        dataset = PowScanDataset(
            split="train",
            data_root=temp_dir,
            test_mode=False
        )
        
        # Check that we have data samples
        print(f"Dataset size: {len(dataset.data_list)}")
        
        # Try to load the sample
        try:
            data = dataset.get_data(0)
            print(f"Sample 0: coord shape = {data['coord'].shape}")
            assert data['coord'].shape[0] > 0, "Coordinate array should not be empty"
        except Exception as e:
            print(f"Error loading sample: {e}")
            return False
        
        print("Edge cases test passed!")
        return True


def main():
    """Main test function"""
    print("Running PowScan dataset fix verification tests...")
    
    # Run tests
    test1_passed = test_empty_grid_handling()
    test2_passed = test_edge_cases()
    
    if test1_passed and test2_passed:
        print("\nAll tests passed! The fix is working correctly.")
        return 0
    else:
        print("\nSome tests failed. Please check the implementation.")
        return 1


if __name__ == "__main__":
    sys.exit(main())