import numpy as np
import argparse
import logging
import os
import sys
import laspy
from configs._base_.dataset.powscan import CLASS2ID

def validate_point_cloud(xyz, labels=None):
    """验证点云数据有效性"""
    if xyz.size == 0:
        raise ValueError("输入点云数据为空")
    if xyz.shape[1] < 3:
        raise ValueError(f"点云有效维度不足3维，当前维度为{xyz.shape[1]}")
    if labels is not None:
        if len(labels.shape) != 1:
            raise ValueError(f"分类号应为1维数组，当前维度为{labels.shape}")
        if xyz.shape[0] != labels.shape[0]:
            raise ValueError(f"坐标数据点数({xyz.shape[0]})与分类号数量({labels.shape[0]})不一致")

def get_colormap(cmap_path=None):
    """获取颜色映射表（适配PowScan分类体系）"""
    if cmap_path:
        colormap = np.load(cmap_path)
        if colormap.shape[1] != 3 or colormap.min() < 0 or colormap.max() > 1:
            raise ValueError("颜色映射表应为Nx3数组，且值在0-1范围内")
        return colormap
    # 默认使用与preprocess_powscan一致的调色板
    return np.array([
        [0.1216, 0.4667, 0.7055],  # blue
        [1.0000, 0.4980, 0.0549],  # orange
        [0.1725, 0.6275, 0.1725],  # green
        [0.8392, 0.1529, 0.1569],  # red
        [0.5804, 0.4039, 0.7412],  # purple
        [0.5490, 0.3373, 0.2941],  # brown
        [0.8902, 0.4667, 0.7608],  # pink
        [0.4980, 0.4980, 0.4980],  # gray
        [0.7373, 0.7412, 0.1333],  # lime
        [0.0902, 0.7451, 0.8118],  # cyan
        [0.9451, 0.7686, 0.0588],  # 新增黄色
        [0.0941, 0.4549, 0.8039]   # 新增深蓝
    ])

def main():
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

    # 解析命令行参数
    parser = argparse.ArgumentParser(description='转换PowScan预处理数据为LAS格式')
    parser.add_argument('-c', '--coords', required=True, help='坐标文件路径(.npy)')
    parser.add_argument('-l', '--labels', required=True, help='标签文件路径(.npy)')
    parser.add_argument('-o', '--output', required=True, help='输出LAS文件路径')
    parser.add_argument('--colormap', help='自定义颜色映射numpy文件路径（Nx3 RGB数组）')
    args = parser.parse_args()

    try:
        # 检查输入文件是否存在
        if not os.path.exists(args.coords):
            raise FileNotFoundError(f"坐标文件不存在: {args.coords}")
        if not os.path.exists(args.labels):
            raise FileNotFoundError(f"标签文件不存在: {args.labels}")

        logging.info(f"开始处理坐标文件: {args.coords}")
        logging.info(f"开始处理标签文件: {args.labels}")
        
        # 加载数据并验证
        logging.info("加载输入文件")
        # 记录输入元数据
        logging.info(f"加载坐标文件: {args.coords}")
        coords = np.load(args.coords)
        logging.info(f"坐标数据 - 形状: {coords.shape} 类型: {coords.dtype}")
        
        logging.info(f"加载标签文件: {args.labels}")
        labels = np.load(args.labels)
        logging.info(f"标签数据 - 形状: {labels.shape} 类型: {labels.dtype}")
        
        # 记录统计信息
        logging.info(f"坐标值域: X({np.min(coords[...,0]):.2f}-{np.max(coords[...,0]):.2f}) "
                     f"Y({np.min(coords[...,1]):.2f}-{np.max(coords[...,1]):.2f}) "
                     f"Z({np.min(coords[...,2]):.2f}-{np.max(coords[...,2]):.2f})")
        unique_labels, counts = np.unique(labels, return_counts=True)
        logging.info(f"标签分布: {dict(zip(unique_labels, counts))}")
        
        # 验证文件对匹配性
        if coords.shape[0] != labels.size:
            raise ValueError(f"坐标文件点数({coords.shape[0]})与标签数量({labels.size})不匹配")
            
        # 维度规范化处理
        if coords.ndim == 4:
            logging.info(f"输入坐标维度: {coords.shape} - 自动展平前三维")
            xyz = coords.reshape(-1, 3)
        elif coords.ndim == 2 and coords.shape[1] >= 3:
            logging.info(f"输入坐标维度: {coords.shape} - 自动取前3列")
            xyz = coords[:, :3]
        else:
            raise ValueError(f"不支持的坐标维度: {coords.shape}，期望四维数组或二维N×3及以上数组")
            
        validate_point_cloud(xyz, labels)

        # 加载颜色映射表
        colormap = get_colormap(args.colormap)
        logging.info(f"使用颜色映射表包含{len(colormap)}种颜色")
        
        # 验证分类号有效性
        valid_classes = set(CLASS2ID.values())
        invalid_labels = [l for l in np.unique(labels) if l not in valid_classes]
        if invalid_labels:
            raise ValueError(f"发现无效分类号: {invalid_labels}，有效范围为{list(valid_classes)}")
            
        # 分配颜色
        colors_float = colormap[labels]  # 直接使用分类号索引
        colors = (np.clip(colors_float, 0.0, 1.0) * 65535).astype(np.uint16)

        # 创建LAS文件
        logging.info("创建LAS文件...")
        # 从配置文件导入CRS参数
        from configs._base_.dataset.powscan import LAS_CRS
        
        header = laspy.LasHeader(version="1.4", point_format=7)
        header.offsets = np.min(xyz, axis=0)
        header.scales = [0.001, 0.001, 0.001]  # 毫米精度
        
        logging.info(f"CRS配置: {LAS_CRS['description']}")
        logging.info(f"全局偏移量: {header.offsets}")
        logging.info(f"坐标参考系统: {LAS_CRS['proj4']}")

        las = laspy.LasData(header)
        las.x = xyz[:, 0]
        las.y = xyz[:, 1]
        las.z = xyz[:, 2]
        
        las.classification = labels.astype(np.uint8)
        las.red = colors[:, 0]
        las.green = colors[:, 1]
        las.blue = colors[:, 2]

        # 创建输出目录
        os.makedirs(os.path.dirname(args.output), exist_ok=True)

        # 保存LAS文件
        logging.info(f"保存结果到: {args.output}")
        las.write(args.output)
        
        logging.info("转换完成")
    except laspy.LaspyException as e:
        logging.error(f"LAS输出错误: {str(e)}")
        sys.exit(3)
    except Exception as e:
        logging.exception("未处理的异常发生:")
        sys.exit(1)

if __name__ == "__main__":
    main()