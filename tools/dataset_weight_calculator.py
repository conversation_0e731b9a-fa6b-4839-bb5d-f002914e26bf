"""
Dataset Weight Calculator for Point Cloud Semantic Segmentation

This tool calculates class weights for point cloud semantic segmentation datasets
using the formula: Wi = a * ln(N/ni)
where a = 1.00644, N is total number of points, ni is number of points in class i.

Supports single dataset and multi-dataset joint statistics with normalization.
"""

import numpy as np
import torch
from collections import Counter, defaultdict
from typing import Dict, List, Tuple, Union
import argparse
import json
import os

# Import only the default dataset and builder
from pointcept.datasets.defaults import DefaultDataset
from pointcept.datasets.builder import DATASETS


class ClassMapper:
    """Map class labels between different dataset formats"""
    
    def __init__(self, mapping_dict: Dict[int, int] = None):
        """
        Initialize class mapper
        
        Args:
            mapping_dict: Dictionary mapping from source class IDs to target class IDs
        """
        self.mapping_dict = mapping_dict or {}
    
    def map_labels(self, labels: np.ndarray) -> np.ndarray:
        """
        Map labels according to the mapping dictionary
        
        Args:
            labels: Array of labels to map
            
        Returns:
            Mapped labels array
        """
        if not self.mapping_dict:
            return labels
            
        mapped_labels = labels.copy()
        for src_cls, tgt_cls in self.mapping_dict.items():
            mapped_labels[labels == src_cls] = tgt_cls
            
        return mapped_labels


class DatasetWeightCalculator:
    """Calculate class weights for point cloud semantic segmentation datasets
    
    Supports exclusion of specific classes from total point count when calculating weights.
    Excluded classes are skipped during statistics accumulation and do not contribute
    to the total point count used for frequency calculation.
    """
    
    def __init__(self, log_base: str = "natural", exclude_classes: List[int] = None):
        """
        Initialize the weight calculator
        
        Args:
            log_base: Base for logarithm calculation ("natural", "10", or custom float)
            exclude_classes: List of class indices to exclude from total point count
                            when calculating weights. These classes will be skipped
                            during statistics accumulation.
        """
        self.log_base = log_base
        self.exclude_classes = set(exclude_classes) if exclude_classes else set()
        self.class_frequencies = defaultdict(int)
        self.total_points = 0
        self.class_mappers = {}  # 存储每个数据集的类别映射器
        
    def _get_log_func(self):
        """Get logarithm function based on base setting"""
        if self.log_base == "natural":
            return np.log
        elif self.log_base == "10":
            return np.log10
        else:
            base = float(self.log_base)
            return lambda x: np.log(x) / np.log(base)
    
    def add_dataset(self, dataset_type: str, data_root: str, split: str = "train",
                   ignore_index: int = -1, dataset_name: str = None, **kwargs):
        """
        Add a dataset to statistics by directly specifying dataset type
        
        Args:
            dataset_type: Type of dataset (e.g., "DefaultDataset", "ScanNetDataset")
            data_root: Root path for dataset
            split: Dataset split (default: "train")
            ignore_index: Index to ignore in statistics (default: -1)
            dataset_name: Name of the dataset (for class mapping)
            **kwargs: Additional arguments for dataset constructor
        """
        # Create dataset instance using the registry
        dataset_cfg = dict(
            type=dataset_type,
            split=split,
            data_root=data_root,
            ignore_index=ignore_index,
            **kwargs
        )
        
        dataset = DATASETS.build(dataset_cfg)
            
        # Use dataset type as dataset name if not provided
        if dataset_name is None:
            dataset_name = dataset_type
            
        self._accumulate_statistics(dataset, dataset_name)
    
    def _accumulate_statistics(self, dataset, dataset_name: str = None):
        """
        Accumulate class frequency statistics from dataset
        
        Args:
            dataset: Point cloud dataset instance
            dataset_name: Name of the dataset (for class mapping)
        """
        print(f"Processing dataset with {len(dataset)} samples...")
        
        for i in range(len(dataset)):
            if i % 100 == 0:
                print(f"Processed {i}/{len(dataset)} samples")
                
            data = dataset[i]
            if 'segment' in data:
                segment = data['segment']
                if isinstance(segment, torch.Tensor):
                    segment = segment.numpy()
                
                # Count class frequencies
                unique, counts = np.unique(segment, return_counts=True)
                for cls, count in zip(unique, counts):
                    # Skip ignore index and excluded classes
                    if cls != dataset.ignore_index and cls not in self.exclude_classes:
                        self.class_frequencies[cls] += count
                        self.total_points += count
            else:
                print(f"Warning: Sample {i} has no segment data")
    
    def calculate_weights(self) -> Dict[int, float]:
        """
        Calculate class weights using the formula Wi = a * ln(N/ni)
        where a = 1.00644, N is total number of points, ni is number of points in class i
        
        Returns:
            Dictionary mapping class indices to weights
        """
        if self.total_points == 0:
            raise ValueError("No data processed. Add datasets first.")
            
        weights = {}
        a = 1.00644
        
        for cls, freq in self.class_frequencies.items():
            # Apply weight formula: Wi = a * ln(N/ni)
            # Handle case where freq is 0 to avoid division by zero
            if freq <= 0:
                weight = a * np.log(self.total_points)  # Use max value as fallback
            else:
                weight = a * np.log(self.total_points / freq)
            
            weights[cls] = weight
            
        return weights
    
    def normalize_weights(self, weights: Dict[int, float], method: str = "min") -> Dict[int, float]:
        """
        Normalize weights
        
        Args:
            weights: Dictionary of class weights
            method: Normalization method ("min", "max", "sum", or "none")
            
        Returns:
            Normalized weights dictionary
        """
        if not weights:
            return weights
            
        if method == "min":
            min_weight = min(weights.values())
            return {cls: w / min_weight for cls, w in weights.items()}
        elif method == "max":
            max_weight = max(weights.values())
            return {cls: w / max_weight for cls, w in weights.items()}
        elif method == "sum":
            sum_weights = sum(weights.values())
            return {cls: w / sum_weights for cls, w in weights.items()}
        elif method == "none":
            return weights
        else:
            raise ValueError(f"Unknown normalization method: {method}")
    
    def get_class_statistics(self) -> Dict:
        """
        Get detailed class statistics
        
        Returns:
            Dictionary with class frequencies and percentages
        """
        stats = {}
        for cls, freq in self.class_frequencies.items():
            percentage = (freq / self.total_points) * 100 if self.total_points > 0 else 0
            stats[cls] = {
                'frequency': freq,
                'percentage': percentage
            }
        return stats
    
    def save_weights_config(self, weights: Dict[int, float], output_path: str,
                          normalize_method: str = "min"):
        """
        Save weights to a config file compatible with Pointcept
        
        Args:
            weights: Class weights dictionary
            output_path: Path to save config file
            normalize_method: Normalization method for final weights
        """
        # Normalize weights
        normalized_weights = self.normalize_weights(weights, normalize_method)
        
        # Sort by class index
        sorted_weights = [normalized_weights[cls] for cls in sorted(normalized_weights.keys())]
        
        # Create config content
        config_content = {
            "class_weights": sorted_weights
        }
        
        # Save as JSON
        with open(output_path, 'w') as f:
            json.dump(config_content, f, indent=2)
        
        print(f"Weights saved to {output_path}")
        print("Weights array:")
        print(sorted_weights)
    
    def validate_formula(self, weights: Dict[int, float], frequencies: Dict[int, float],
                       tolerance: float = 1e-4) -> bool:
        """
        Validate that weights were calculated using the correct formula Wi = a * ln(N/ni)
        where a = 1.00644, N is total number of points, ni is number of points in class i
        
        Args:
            weights: Calculated class weights
            frequencies: Class frequencies (0-1)
            tolerance: Acceptable difference tolerance
            
        Returns:
            True if formula is valid, False otherwise
        """
        a = 1.00644
        total_points = sum(frequencies.values())
        
        for cls, weight in weights.items():
            if cls in frequencies:
                freq = frequencies[cls]
                
                # Calculate expected weight: Wi = a * ln(N/ni)
                # Handle case where freq is 0 to avoid division by zero
                if freq <= 0:
                    expected_weight = a * np.log(total_points)  # Use max value as fallback
                else:
                    expected_weight = a * np.log(total_points / freq)
                
                if abs(weight - expected_weight) > tolerance:
                    print(f"Mismatch for class {cls}: expected {expected_weight}, got {weight}")
                    return False
        return True


def main():
    """
    Main function to calculate dataset class weights with optional class exclusion.
    
    Example usage:
        python dataset_weight_calculator.py DefaultDataset --data_roots /path/to/data \\
            --exclude_classes "0,255" --output weights.json
    
    The --exclude_classes parameter allows excluding specific classes from the total
    point count used for weight calculation. Excluded classes are completely skipped
    during statistics accumulation.
    """
    parser = argparse.ArgumentParser(description="Calculate dataset class weights")
    parser.add_argument("dataset_types", nargs="+", help="Dataset types (e.g., DefaultDataset, ScanNetDataset)")
    parser.add_argument("--data_roots", nargs="+", required=True, help="Dataset root paths")
    parser.add_argument("--splits", nargs="*", default=["train"], help="Dataset splits (default: train)")
    parser.add_argument("--ignore_index", type=int, default=-1, help="Ignore index (default: -1)")
    parser.add_argument("--exclude_classes", type=str, default="",
                       help="Comma-separated list of class indices to exclude from total point count (e.g., '0,1,255')")
    parser.add_argument("--class_mappings", nargs="*", help="Class mapping files (JSON format, optional)")
    parser.add_argument("--log_base", default="natural",
                       help="Logarithm base (natural, 10, or custom float)")
    parser.add_argument("--normalize", default="none",
                       choices=["min", "max", "sum", "none"],
                       help="Normalization method (default: none)")
    parser.add_argument("--output", required=True, help="Output config file path")
    
    args = parser.parse_args()
    
    # Parse exclude_classes parameter
    exclude_classes = []
    if args.exclude_classes:
        try:
            exclude_classes = [int(cls.strip()) for cls in args.exclude_classes.split(',') if cls.strip()]
        except ValueError as e:
            print(f"Error parsing exclude_classes: {e}")
            print("Please provide comma-separated integers (e.g., '0,1,255')")
            return
    
    # Parse splits parameter - handle comma-separated strings
    processed_splits = []
    for split_arg in args.splits:
        # If the split argument contains commas, split it into individual splits
        if ',' in split_arg:
            individual_splits = [s.strip() for s in split_arg.split(',') if s.strip()]
            processed_splits.extend(individual_splits)
        else:
            processed_splits.append(split_arg.strip())
    
    args.splits = processed_splits
    
    # Create calculator
    calculator = DatasetWeightCalculator(log_base=args.log_base, exclude_classes=exclude_classes)
    
    # Process datasets
    if len(args.splits) == 1 and len(args.dataset_types) > 1:
        splits = args.splits * len(args.dataset_types)
    else:
        splits = args.splits
        
    if len(args.data_roots) == 1 and len(args.dataset_types) > 1:
        data_roots = args.data_roots * len(args.dataset_types)
    else:
        data_roots = args.data_roots
        
    class_mappings = args.class_mappings or [None] * len(args.dataset_types)
    if len(class_mappings) == 1 and len(args.dataset_types) > 1:
        class_mappings = class_mappings * len(args.dataset_types)
        
    # Show excluded classes information
    if exclude_classes:
        print(f"Excluding classes from total point count: {exclude_classes}")
    else:
        print("No classes excluded from total point count")
    
    for i, (dataset_type, data_root, split) in enumerate(zip(args.dataset_types, data_roots, splits)):
        print(f"Processing dataset: {dataset_type} at {data_root}")
        
        # Load class mapping if provided
        class_mapping = None
        if class_mappings[i] is not None:
            with open(class_mappings[i], 'r') as f:
                class_mapping = json.load(f)
            print(f"Using class mapping from: {class_mappings[i]}")
            
        dataset_name = f"{dataset_type}_{i}" if len(args.dataset_types) > 1 else dataset_type
        calculator.add_dataset(dataset_type, data_root, split, args.ignore_index, dataset_name)
    
    # Calculate weights
    weights = calculator.calculate_weights()
    
    # Show statistics
    stats = calculator.get_class_statistics()
    print("\nClass Statistics:")
    for cls in sorted(stats.keys()):
        stat = stats[cls]
        print(f"Class {cls}: {stat['frequency']} points ({stat['percentage']:.4f}%)")
    
    # Validate formula
    frequencies = {cls: stat['frequency'] / calculator.total_points for cls, stat in stats.items()}
    is_valid = calculator.validate_formula(weights, frequencies)
    if is_valid:
        print("\nFormula validation: PASSED")
    else:
        print("\nFormula validation: FAILED")
    
    # Save config
    calculator.save_weights_config(weights, args.output, args.normalize)


if __name__ == "__main__":
    main()