#!/usr/bin/env python3
"""
SemanticKITTI点云结果转换为PLY格式工具
支持将点云数据和预测标签融合生成带颜色的PLY文件
"""

import numpy as np
import argparse
import logging
import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tools.semantic_kitti_colormap import get_semantic_kitti_colors, IGNORE_COLOR


def validate_point_cloud(xyz, labels=None):
    """验证点云数据有效性"""
    if xyz.size == 0:
        raise ValueError("输入点云数据为空")
    if xyz.shape[1] != 3:
        raise ValueError(f"点云维度应为3维，当前维度为{xyz.shape[1]}")
    if labels is not None:
        if len(labels.shape) != 1:
            raise ValueError(f"分类号应为1维数组，当前维度为{labels.shape}")
        if xyz.shape[0] != labels.shape[0]:
            raise ValueError(f"坐标数据点数({xyz.shape[0]})与分类号数量({labels.shape[0]})不一致")


def write_ply_binary_with_classification(filename, xyz, colors, labels):
    """
    以二进制格式写入PLY文件，包含classification属性（高度优化版本）
    
    Args:
        filename: 输出文件路径
        xyz: 点云坐标 (N, 3)
        colors: RGB颜色值 (N, 3)
        labels: 分类标签 (N,)
    """
    with open(filename, 'wb') as f:
        # 写入PLY头部
        header = f"""ply
format binary_little_endian 1.0
comment Generated by semantic_kitti_result2ply.py
element vertex {len(xyz)}
property float x
property float y
property float z
property uchar red
property uchar green
property uchar blue
property int classification
end_header
"""
        f.write(header.encode('utf-8'))
        
        # 准备所有数据
        # 坐标数据 (N, 3) float32
        xyz_data = xyz.astype(np.float32)
        
        # 颜色数据 (N, 3) uint8
        colors_data = colors.astype(np.uint8)
        
        # 标签数据 (N,) int32
        labels_data = labels.astype(np.int32)
        
        # 将所有数据按点的顺序组合并写入
        # 每个点的数据格式: x(float32), y(float32), z(float32), r(uint8), g(uint8), b(uint8), classification(int32)
        # 为了提高性能，我们将所有数据组合成一个大的字节数组一次性写入
        for i in range(len(xyz)):
            # 组合单个点的所有数据
            point_data = (
                xyz_data[i].tobytes() +      # 3个float32 (12 bytes)
                colors_data[i].tobytes() +   # 3个uint8 (3 bytes)
                labels_data[i].tobytes()     # 1个int32 (4 bytes)
            )
            f.write(point_data)


def read_semantic_kitti_point_cloud(bin_file):
    """
    读取SemanticKITTI点云数据
    
    Args:
        bin_file: .bin文件路径
        
    Returns:
        numpy数组，点云坐标 (N, 3)
    """
    # SemanticKITTI点云数据格式为float32，每个点包含x, y, z, intensity
    scan = np.fromfile(bin_file, dtype=np.float32)
    # 重塑为(N, 4)并取前3列作为坐标
    points = scan.reshape((-1, 4))[:, :3]
    return points


def read_semantic_kitti_labels(label_file):
    """
    读取SemanticKITTI标签数据
    
    Args:
        label_file: .label文件路径
        
    Returns:
        numpy数组，标签 (N,)
    """
    # SemanticKITTI标签数据格式为uint32
    labels = np.fromfile(label_file, dtype=np.uint32)
    # 取低16位作为语义标签
    semantic_labels = labels & 0xFFFF
    return semantic_labels


def map_labels_to_training_ids(labels, ignore_index=-1):
    """
    将原始标签映射到训练时使用的标签ID
    
    Args:
        labels: 原始标签数组
        ignore_index: 忽略标签的ID
        
    Returns:
        numpy数组，映射后的标签
    """
    # SemanticKITTI标签映射表（与pointcept/datasets/semantic_kitti.py中一致）
    learning_map = {
        0: ignore_index,   # "unlabeled"
        1: ignore_index,   # "outlier" mapped to "unlabeled"
        10: 0,   # "car"
        11: 1,   # "bicycle"
        13: 4,   # "bus" mapped to "other-vehicle"
        15: 2,   # "motorcycle"
        16: 4,   # "on-rails" mapped to "other-vehicle"
        18: 3,   # "truck"
        20: 4,   # "other-vehicle"
        30: 5,   # "person"
        31: 6,   # "bicyclist"
        32: 7,   # "motorcyclist"
        40: 8,   # "road"
        44: 9,   # "parking"
        48: 10,  # "sidewalk"
        49: 11,  # "other-ground"
        50: 12,  # "building"
        51: 13,  # "fence"
        52: ignore_index,  # "other-structure" mapped to "unlabeled"
        60: 8,   # "lane-marking" to "road"
        70: 14,  # "vegetation"
        71: 15,  # "trunk"
        72: 16,  # "terrain"
        80: 17,  # "pole"
        81: 18,  # "traffic-sign"
        99: ignore_index,  # "other-object" to "unlabeled"
        252: 0,  # "moving-car" to "car"
        253: 6,  # "moving-bicyclist" to "bicyclist"
        254: 5,  # "moving-person" to "person"
        255: 7,  # "moving-motorcyclist" to "motorcyclist"
        256: 4,  # "moving-on-rails" mapped to "other-vehicle"
        257: 4,  # "moving-bus" mapped to "other-vehicle"
        258: 3,  # "moving-truck" to "truck"
        259: 4,  # "moving-other"-vehicle to "other-vehicle"
    }
    
    # 应用映射
    mapped_labels = np.vectorize(learning_map.get)(labels, ignore_index)
    return mapped_labels.astype(np.int32)


def process_sequence(data_root, result_root, output_root, sequence, ignore_index=-1, use_original_labels=False):
    """
    处理单个序列的点云数据
    
    Args:
        data_root: 原始数据根目录
        result_root: 预测结果根目录或原始标签根目录
        output_root: 输出根目录
        sequence: 序列号（如'00'）
        ignore_index: 忽略标签的ID
        use_original_labels: 是否使用原始标签文件而不是预测标签
    """
    logging.info(f"处理序列 {sequence}")
    
    # 构建路径
    velodyne_dir = os.path.join(data_root, "dataset", "sequences", sequence, "velodyne")
    
    # 根据use_original_labels参数确定标签文件路径
    if use_original_labels:
        # 使用原始标签文件
        label_dir = os.path.join(data_root, "dataset", "sequences", sequence, "labels")
        logging.info(f"使用原始标签文件: {label_dir}")
    else:
        # 使用预测标签文件
        label_dir = os.path.join(result_root, "sequences", sequence, "predictions")
        logging.info(f"使用预测标签文件: {label_dir}")
    
    output_dir = os.path.join(output_root, "sequences", sequence)
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取所有点云文件
    if not os.path.exists(velodyne_dir):
        logging.warning(f"点云目录不存在: {velodyne_dir}")
        return
        
    bin_files = [f for f in os.listdir(velodyne_dir) if f.endswith(".bin")]
    bin_files.sort()
    
    logging.info(f"找到 {len(bin_files)} 个点云文件")
    
    # 为了测试，只处理前1个文件
    # bin_files = bin_files[:1]
    # logging.info(f"为测试目的，只处理前 {len(bin_files)} 个文件")
    
    for bin_file in bin_files:
        # 构建文件路径
        bin_path = os.path.join(velodyne_dir, bin_file)
        label_file = os.path.join(label_dir, bin_file.replace(".bin", ".label"))
        ply_file = os.path.join(output_dir, bin_file.replace(".bin", ".ply"))
        
        # 检查文件是否存在
        if not os.path.exists(bin_path):
            logging.warning(f"点云文件不存在: {bin_path}")
            continue
            
        if not os.path.exists(label_file):
            logging.warning(f"标签文件不存在: {label_file}")
            continue
            
        try:
            # 读取点云数据
            xyz = read_semantic_kitti_point_cloud(bin_path)
            
            # 读取标签数据
            original_labels = read_semantic_kitti_labels(label_file)
            
            # 验证数据一致性
            if len(xyz) != len(original_labels):
                logging.warning(f"点云和标签数量不一致: {bin_file} (点云: {len(xyz)}, 标签: {len(original_labels)})")
                continue
                
            # 映射标签到训练ID
            mapped_labels = map_labels_to_training_ids(original_labels, ignore_index)
            
            # 获取颜色映射
            colors = get_semantic_kitti_colors(mapped_labels, ignore_index)
            
            # 写入PLY文件
            write_ply_binary_with_classification(ply_file, xyz, colors, mapped_labels)
            logging.info(f"成功生成PLY文件: {ply_file}")
            
        except Exception as e:
            logging.error(f"处理文件 {bin_file} 时出错: {str(e)}")
            continue


def main():
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

    # 解析命令行参数
    parser = argparse.ArgumentParser(description='转换SemanticKITTI点云数据和预测标签为PLY格式')
    parser.add_argument('--data_root', required=True, help='原始数据根目录 (data/semantic_kitti)')
    parser.add_argument('--result_root', required=True, help='预测结果根目录 (exp/semantic_kitti/submit/result/submit) 或原始标签根目录 (data/semantic_kitti)')
    parser.add_argument('--output_root', required=True, help='输出PLY文件根目录')
    parser.add_argument('--sequence', help='处理特定序列 (如 00, 01, ...), 默认处理所有序列')
    parser.add_argument('--ignore_index', type=int, default=-1, help='忽略标签的ID (默认: -1)')
    parser.add_argument('--use_original_labels', action='store_true', help='使用原始标签文件而不是预测标签')
    args = parser.parse_args()

    try:
        # 检查输入目录是否存在
        if not os.path.exists(args.data_root):
            raise FileNotFoundError(f"原始数据目录不存在: {args.data_root}")
            
        if not os.path.exists(args.result_root):
            raise FileNotFoundError(f"结果目录不存在: {args.result_root}")

        logging.info(f"开始处理SemanticKITTI数据:")
        logging.info(f"  原始数据目录: {args.data_root}")
        logging.info(f"  结果目录: {args.result_root}")
        logging.info(f"  输出目录: {args.output_root}")
        logging.info(f"  使用原始标签: {args.use_original_labels}")
        
        # 确定要处理的序列
        if args.sequence:
            sequences = [args.sequence]
        else:
            # 获取所有序列
            dataset_dir = os.path.join(args.data_root, "dataset", "sequences")
            if not os.path.exists(dataset_dir):
                raise FileNotFoundError(f"数据集目录不存在: {dataset_dir}")
            sequences = [s for s in os.listdir(dataset_dir) if s.isdigit() and len(s) == 2]
            sequences.sort()
            
        logging.info(f"处理序列: {sequences}")
        
        # 处理每个序列
        for sequence in sequences:
            process_sequence(args.data_root, args.result_root, args.output_root, sequence, args.ignore_index, args.use_original_labels)
            
        logging.info("所有序列处理完成")

    except Exception as e:
        logging.error(f"处理失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()