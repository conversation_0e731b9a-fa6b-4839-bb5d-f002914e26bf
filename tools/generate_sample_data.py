"""
Generate Sample PowScan Data for Testing

Author: Your Name
Please cite our work if the code is helpful to you.
"""

import os
import numpy as np
import laspy


def create_sample_las_file(filepath, points, classification=None):
    """创建示例LAS文件"""
    # 创建新的LAS文件
    header = laspy.LasHeader(version="1.2", point_format=3)
    header.offsets = np.min(points, axis=0) if len(points) > 0 else np.array([0, 0, 0])
    header.scales = np.array([0.01, 0.01, 0.01])
    
    las = laspy.LasData(header)
    las.x = points[:, 0]
    las.y = points[:, 1]
    las.z = points[:, 2]
    
    if classification is not None:
        las.classification = classification
    
    las.write(filepath)


def generate_sample_data(root_dir="data/powscan"):
    """生成示例数据"""
    print(f"Generating sample PowScan data in {root_dir}")
    
    # 创建目录结构
    scene_dir = os.path.join(root_dir, "数据1")
    raw_dir = os.path.join(scene_dir, "原始切档目录")
    class1_dir = os.path.join(scene_dir, "类别1-铁塔")
    class2_dir = os.path.join(scene_dir, "类别2-电线")
    
    os.makedirs(raw_dir, exist_ok=True)
    os.makedirs(class1_dir, exist_ok=True)
    os.makedirs(class2_dir, exist_ok=True)
    
    # 生成原始点云数据
    # 创建一些随机点云数据
    np.random.seed(42)  # 固定随机种子以确保可重现性
    
    # 原始点云 (1000个点)
    raw_points = np.random.rand(1000, 3) * 100  # 100x100x100的空间
    raw_file = os.path.join(raw_dir, "1_2(1_2).las")
    create_sample_las_file(raw_file, raw_points)
    print(f"Created raw point cloud: {raw_file}")
    
    # 生成分类数据
    # 铁塔点 (随机选择100个点)
    tower_indices = np.random.choice(1000, 100, replace=False)
    tower_points = raw_points[tower_indices]
    tower_file = os.path.join(class1_dir, "1_2(1_2)铁塔.las")
    create_sample_las_file(tower_file, tower_points)
    print(f"Created tower classification: {tower_file}")
    
    # 电线点 (随机选择150个点)
    wire_indices = np.random.choice(1000, 150, replace=False)
    wire_points = raw_points[wire_indices]
    wire_file = os.path.join(class2_dir, "1_2(1_2)电线.las")
    create_sample_las_file(wire_file, wire_points)
    print(f"Created wire classification: {wire_file}")
    
    print("Sample data generation completed!")


if __name__ == "__main__":
    generate_sample_data()