#!/usr/bin/env python3
"""
Script to check label distribution in PowScan dataset and identify invalid labels.
This helps diagnose CUDA index out of bounds errors caused by invalid label values.
"""

import numpy as np
from pointcept.datasets import PowScanDataset

def main():
    print("Loading PowScan dataset for training split...")
    
    try:
        # Initialize the dataset
        dataset = PowScanDataset(split='train', data_root='data/powscan')
        print(f"Dataset contains {len(dataset)} samples")
        
        # Check label distribution
        label_counts = {}
        invalid_labels = set()
        expected_labels = set(range(12))  # Labels should be 0-11
        
        # Check first 100 samples or all if less than 100
        num_samples_to_check = len(dataset)#min(100, len(dataset))
        print(f"Checking first {num_samples_to_check} samples...")
        
        for i in range(num_samples_to_check):
            try:
                data = dataset.get_data(i)
                labels = data['segment']
                
                # Get unique labels in this sample
                unique_labels = np.unique(labels)
                
                # Check for invalid labels
                for label in unique_labels:
                    if label not in expected_labels:
                        invalid_labels.add(label)
                
                # Count label occurrences
                for label in unique_labels:
                    label_counts[label] = label_counts.get(label, 0) + 1
                
                if i % 10 == 0:
                    print(f"Processed sample {i}/{num_samples_to_check}")
                    
            except Exception as e:
                print(f"Error processing sample {i}: {e}")
                continue
        
        print("\n=== Label Distribution ===")
        for label in sorted(label_counts.keys()):
            print(f"Label {label}: {label_counts[label]} samples contain this label")
        
        print("\n=== Invalid Labels Found ===")
        if invalid_labels:
            print(f"WARNING: Found {len(invalid_labels)} invalid label(s): {sorted(invalid_labels)}")
            print("These labels are outside the expected range (0-11) and may cause CUDA errors.")
        else:
            print("No invalid labels found in the checked samples.")
            
        # Also check the overall label range
        all_labels = list(label_counts.keys())
        min_label = min(all_labels) if all_labels else 0
        max_label = max(all_labels) if all_labels else 0
        print(f"\nLabel range: {min_label} to {max_label}")
        
        if min_label < 0 or max_label >= 12:
            print("ERROR: Labels outside valid range (0-11) detected!")
        else:
            print("All labels are within valid range (0-11).")
            
    except Exception as e:
        print(f"Error loading dataset: {e}")
        print("Make sure the dataset path 'data/powscan' exists and is properly formatted.")

if __name__ == "__main__":
    main()