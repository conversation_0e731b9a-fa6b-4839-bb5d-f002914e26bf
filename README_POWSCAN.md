# PowScan 数据集使用说明

## 数据集结构

PowScan数据集具有以下目录结构：

```
data/powscan/
├── 数据1/
│   ├── 原始切档数据/
│   │   ├── 1_2(1_2).las
│   │   └── ...
│   ├── 类别1-导线/
│   │   ├── 1_2(1_2)_导线.las
│   │   └── ...
│   ├── 类别2-中等植被点/
│   │   ├── 1_2(1_2)_中等植被点.las
│   │   └── ...
│   └── ...
├── 数据2/
│   ├── 原始切档数据/
│   ├── 类别1-导线/
│   ├── 类别2-中等植被点/
│   └── ...
└── processed/ (预处理后生成)
    ├── 数据1/
    │   ├── 1_2(1_2).npy
    │   └── ...
    ├── 数据2/
    │   ├── 1_2(1_2).npy
    │   └── ...
    └── class_mapping.json
```

## 预处理步骤

1. 安装必要的依赖：
   ```bash
   pip install laspy scipy
   ```

2. 运行预处理脚本：
   ```bash
   python tools/preprocess_powscan.py --data_root data/powscan --num_workers 4
   ```

   参数说明：
   - `--data_root`: PowScan数据集根目录
   - `--num_workers`: 并行处理的工作进程数（可选，默认为1）
   - `--output_root`: 输出目录（可选，默认为`data_root/processed`）

## 训练模型

预处理完成后，可以使用以下命令训练模型：

```bash
# 使用默认配置训练
python tools/train_powscan.py

# 或者指定配置文件训练
python tools/train.py --config configs/powscan/semseg-pt-v1m1-0-base.py

# 使用基于DefaultSegmentor的配置训练
python tools/train.py --config configs/powscan/semseg-pt-v2m2-0-lovasz.py
```

## 测试模型

使用以下命令测试模型：

```bash
python tools/test.py --config configs/powscan/semseg-pt-v1m1-0-base.py --checkpoint path/to/checkpoint.pth
```

## 使用DefaultSegmentor进行分割

本项目提供了一个基于DefaultSegmentor的配置文件，可以用于对PowScan数据集进行分割：

```bash
# 使用提供的配置文件训练模型
python train.py --config configs/powscan/semseg-pt-v2m2-0-lovasz.py
```

配置文件`configs/powscan/semseg-pt-v2m2-0-lovasz.py`包含了以下特性：
- 使用PT-v2m2作为骨干网络
- 支持11个类别（包括未标记点）
- 包含数据增强和预处理步骤
- 使用LovaszLoss和CrossEntropyLoss组合进行训练

您也可以根据需要修改配置文件中的超参数，如学习率、批次大小等。

## 自定义类别

PowScan数据集支持动态类别映射。预处理脚本会自动生成`class_mapping.json`文件，其中包含所有类别名称到ID的映射。

如果需要自定义类别，可以修改`configs/_base_/dataset/powscan.py`文件中的`class_names`列表。

## 数据集类说明

`PowScanDataset`类继承自`DefaultDataset`，提供了以下功能：

- 自动加载预处理后的点云数据和标签
- 支持标准的Pointcept数据流（`__getitem__`返回`dict(coord, segment)`）
- 动态加载类别映射
- 支持缓存机制提高加载效率

## 测试实现

可以运行以下命令测试实现是否正确：

```bash
# 基本功能测试
python tools/test_powscan_complete.py --data_root data/powscan

# 完整功能测试（包括数据加载、配置文件、数据集构建等）
python tools/test_powscan_full.py
```

## 注意事项

1. 预处理过程可能需要较长时间，建议使用多个工作进程加速处理
2. 确保原始LAS文件和分类文件的命名一致
3. 对于缺失的分类文件，预处理脚本会自动跳过并记录警告
4. 未标记的点会被分配标签ID 0
5. 类别映射在预处理时自动生成，运行时动态加载

## 支持的类别

当前支持的类别包括：
- 导线
- 中等植被点
- 铁塔
- 其他线路
- 地面点
- 建筑物点
- 交叉跨越下
- 公路
- 其他
- 临时建筑物
- 交叉跨越上

类别ID从1开始，0保留给未标记点。