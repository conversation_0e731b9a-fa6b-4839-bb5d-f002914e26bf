#!/usr/bin/env python3
"""
Test script to verify the PowScan dataset fixes
"""

import os
import sys
import numpy as np

# Add the project root to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

from pointcept.datasets.powscan import PowScanDataset

def test_fixed_grid_dimensions():
    """Test the fixed grid dimensions calculation"""
    print("Testing fixed grid dimensions calculation...")
    
    # Create a dataset instance
    dataset = PowScanDataset(split="train", data_root="data/powscan")
    
    # Test with different grid counts and spatial extents
    test_cases = [
        (10, np.array([100.0, 50.0, 20.0])),  # Wide in X
        (15, np.array([30.0, 100.0, 40.0])),  # Wide in Y  
        (8, np.array([40.0, 40.0, 100.0])),   # Wide in Z
        (20, np.array([100.0, 100.0, 100.0])) # Equal dimensions
    ]
    
    print("Fixed implementation:")
    for grid_count, spatial_extent in test_cases:
        dims = dataset._calculate_grid_dimensions(grid_count, spatial_extent)
        total_grids = dims[0] * dims[1] * dims[2]
        print(f"  Grid count: {grid_count}, Spatial extent: {spatial_extent}")
        print(f"  Calculated dimensions: {dims}")
        print(f"  Total grids: {total_grids}")
        print(f"  Efficiency: {total_grids}/{grid_count} = {total_grids/grid_count:.2f}")
        print()
    
    # Test with a real file
    test_file = "data/powscan/processed/数据9/17-18(17_18).npy"
    if os.path.exists(test_file):
        print(f"Testing with real file: {test_file}")
        data = np.load(test_file)
        coords = data[:, :3]
        
        min_coord = np.min(coords, axis=0)
        max_coord = np.max(coords, axis=0)
        spatial_extent = max_coord - min_coord
        
        grid_count = max(2, int(np.ceil(data.shape[0] / dataset.MAX_POINTS_PER_SAMPLE)))
        dims = dataset._calculate_grid_dimensions(grid_count, spatial_extent)
        
        print(f"  File shape: {data.shape}")
        print(f"  Spatial extent: {spatial_extent}")
        print(f"  Grid count needed: {grid_count}")
        print(f"  Fixed dimensions: {dims}")
        print(f"  Total grids created: {dims[0] * dims[1] * dims[2]}")
        print(f"  Distribution: X={dims[0]}, Y={dims[1]}, Z={dims[2]}")

def test_improved_grid_splitting():
    """Test the improved grid splitting with safety checks"""
    print("\nTesting improved grid splitting...")
    
    # Test with a real file
    test_file = "data/powscan/processed/数据9/17-18(17_18).npy"
    if not os.path.exists(test_file):
        print(f"Test file {test_file} not found")
        return
    
    # Load the data
    data = np.load(test_file)
    print(f"Loaded file: {test_file}")
    print(f"Data shape: {data.shape}")
    
    # Create dataset and data dict
    dataset = PowScanDataset(split="train", data_root="data/powscan")
    
    data_dict = {
        "coord": data[:, :3].astype(np.float32),
        "strength": data[:, 3:4].astype(np.float32),
        "color": data[:, 4:7].astype(np.float32),
        "segment": data[:, 7].astype(np.int32),
        "name": os.path.basename(test_file)
    }
    
    # Test grid splitting
    print(f"\nTesting improved grid splitting with {data_dict['coord'].shape[0]} points...")
    segments = dataset._split_by_coordinate_grid(data_dict)
    print(f"Created {len(segments)} segments")
    
    total_points = 0
    for i, segment in enumerate(segments):
        point_count = segment["coord"].shape[0]
        total_points += point_count
        print(f"  Segment {i}: {point_count} points")
    
    print(f"\nSummary:")
    print(f"  Original points: {data_dict['coord'].shape[0]}")
    print(f"  Total points in segments: {total_points}")
    print(f"  Point preservation: {total_points/data_dict['coord'].shape[0]*100:.1f}%")

def test_error_handling():
    """Test error handling and minimal sample creation"""
    print("\nTesting error handling...")
    
    dataset = PowScanDataset(split="train", data_root="data/powscan")
    
    # Test minimal sample creation
    minimal_sample = dataset._create_minimal_sample("test")
    print(f"Minimal sample created:")
    print(f"  Coord shape: {minimal_sample['coord'].shape}")
    print(f"  Strength shape: {minimal_sample['strength'].shape}")
    print(f"  Segment shape: {minimal_sample['segment'].shape}")
    print(f"  Name: {minimal_sample['name']}")

def test_dataset_loading():
    """Test dataset loading with fixes"""
    print("\nTesting dataset loading with fixes...")
    
    # Initialize the dataset
    dataset = PowScanDataset(
        split="train",
        data_root="data/powscan",
        test_mode=False
    )
    
    print(f"Dataset size: {len(dataset.data_list)}")
    
    # Test loading a few samples
    test_indices = [0, 100, 500, 1000, 1500]
    
    for idx in test_indices:
        if idx < len(dataset):
            try:
                data = dataset.get_data(idx)
                print(f"Sample {idx}: coord shape = {data['coord'].shape}, name = {data.get('name', 'unknown')}")
                
                # Verify data integrity
                assert data['coord'].shape[0] > 0, "Coordinate array should not be empty"
                assert not np.any(np.isnan(data['coord'])), "Coordinates should not contain NaN"
                assert not np.any(np.isinf(data['coord'])), "Coordinates should not contain Inf"
                
            except Exception as e:
                print(f"Error loading sample {idx}: {e}")
                return False
    
    print("All test samples loaded successfully!")
    return True

def main():
    """Main test function"""
    print("Testing PowScan dataset fixes...")
    
    # Test the fixes
    test_fixed_grid_dimensions()
    test_improved_grid_splitting()
    test_error_handling()
    success = test_dataset_loading()
    
    if success:
        print("\n✅ All tests passed! The fixes are working correctly.")
        print("\nKey improvements:")
        print("1. Fixed grid dimensions calculation for better spatial distribution")
        print("2. Added safety checks to prevent empty grids")
        print("3. Improved error handling with minimal sample fallback")
        print("4. Better memory efficiency through proper grid splitting")
        return 0
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
