#!/usr/bin/env python3
"""
Test script to verify the grid splitting issue
"""

import os
import sys
import numpy as np

# Add the project root to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

from pointcept.datasets.powscan import PowScanDataset

def test_grid_dimensions_issue():
    """Test the grid dimensions calculation issue"""
    print("Testing grid dimensions calculation...")
    
    # Create a dataset instance
    dataset = PowScanDataset(split="train", data_root="data/powscan")
    
    # Test with different grid counts and spatial extents
    test_cases = [
        (10, np.array([100.0, 50.0, 20.0])),  # Wide in X
        (15, np.array([30.0, 100.0, 40.0])),  # Wide in Y  
        (8, np.array([40.0, 40.0, 100.0])),   # Wide in Z
        (20, np.array([100.0, 100.0, 100.0])) # Equal dimensions
    ]
    
    print("Current (buggy) implementation:")
    for grid_count, spatial_extent in test_cases:
        dims = dataset._calculate_grid_dimensions(grid_count, spatial_extent)
        print(f"  Grid count: {grid_count}, Spatial extent: {spatial_extent}")
        print(f"  Calculated dimensions: {dims}")
        print(f"  Total grids: {dims[0] * dims[1] * dims[2]}")
        print()
    
    # Test with a real file to see the impact
    test_file = "data/powscan/processed/数据9/17-18(17_18).npy"
    if os.path.exists(test_file):
        print(f"Testing with real file: {test_file}")
        data = np.load(test_file)
        coords = data[:, :3]
        
        min_coord = np.min(coords, axis=0)
        max_coord = np.max(coords, axis=0)
        spatial_extent = max_coord - min_coord
        
        grid_count = max(2, int(np.ceil(data.shape[0] / dataset.MAX_POINTS_PER_SAMPLE)))
        dims = dataset._calculate_grid_dimensions(grid_count, spatial_extent)
        
        print(f"  File shape: {data.shape}")
        print(f"  Spatial extent: {spatial_extent}")
        print(f"  Grid count needed: {grid_count}")
        print(f"  Current dimensions: {dims}")
        print(f"  Total grids created: {dims[0] * dims[1] * dims[2]}")
        
        # This shows the problem: all grids are created along Y-axis only
        print(f"  Problem: All {dims[1]} grids are along Y-axis only!")

def test_proper_grid_dimensions():
    """Test what the grid dimensions should be"""
    print("\nTesting proper grid dimensions calculation...")
    
    def proper_calculate_grid_dimensions(grid_count, spatial_extent):
        """Proper implementation of grid dimensions calculation"""
        # 根据空间范围的比例分配网格维度
        total_extent = np.sum(spatial_extent)
        if total_extent == 0:
            return [1, 1, 1]
            
        x_ratio = spatial_extent[0] / total_extent
        y_ratio = spatial_extent[1] / total_extent
        z_ratio = spatial_extent[2] / total_extent
        
        # Calculate dimensions based on ratios
        x_dim = max(1, round(grid_count ** (1/3) * (x_ratio ** (1/3))))
        y_dim = max(1, round(grid_count ** (1/3) * (y_ratio ** (1/3))))
        z_dim = max(1, round(grid_count / (x_dim * y_dim)))
        
        # Ensure we don't exceed the grid count
        while x_dim * y_dim * z_dim > grid_count and z_dim > 1:
            z_dim -= 1
        while x_dim * y_dim * z_dim > grid_count and y_dim > 1:
            y_dim -= 1
        while x_dim * y_dim * z_dim > grid_count and x_dim > 1:
            x_dim -= 1
            
        return [x_dim, y_dim, z_dim]
    
    # Test with the same cases
    test_cases = [
        (10, np.array([100.0, 50.0, 20.0])),  # Wide in X
        (15, np.array([30.0, 100.0, 40.0])),  # Wide in Y  
        (8, np.array([40.0, 40.0, 100.0])),   # Wide in Z
        (20, np.array([100.0, 100.0, 100.0])) # Equal dimensions
    ]
    
    print("Proper implementation:")
    for grid_count, spatial_extent in test_cases:
        dims = proper_calculate_grid_dimensions(grid_count, spatial_extent)
        print(f"  Grid count: {grid_count}, Spatial extent: {spatial_extent}")
        print(f"  Calculated dimensions: {dims}")
        print(f"  Total grids: {dims[0] * dims[1] * dims[2]}")
        print()

if __name__ == "__main__":
    test_grid_dimensions_issue()
    test_proper_grid_dimensions()
