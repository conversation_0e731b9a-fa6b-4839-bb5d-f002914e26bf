"""
S3DIS Dataset

Author: <PERSON><PERSON> (<EMAIL>)
Please cite our work if the code is helpful to you.
"""

import os
from .defaults import DefaultDataset
from .builder import DATASETS


@DATASETS.register_module()
class S3DISDataset(DefaultDataset):
    def get_data_name(self, idx):
        remain, room_name = os.path.split(self.data_list[idx % len(self.data_list)])
        remain, area_name = os.path.split(remain)
        return f"{area_name}-{room_name}"
