"""
PowScan Dataset Constants

Author: Your Name
Please cite our work if the code is helpful to you.
"""

import os
import json
import numpy as np

# 默认值 (从配置文件加载)
try:
    from configs._base_.dataset.powscan import CLASS_NAMES, CLASS2ID, CLASS_IDS
except ImportError:
    CLASS_NAMES = []
    CLASS2ID = {}
    CLASS_IDS = []

# 未标记点的标签ID
UNLABELED_ID = 0
IGNORE_INDEX = -1

def load_class_mapping(data_root="data/powscan"):
    """
    加载类别映射
    
    Args:
        data_root: 数据集根目录
    
    Returns:
        tuple: (class_names, class2id, class_ids)
    """
    processed_dir = os.path.join(data_root, "processed")
    mapping_file = os.path.join(processed_dir, "class_mapping.json")
    
    if not os.path.exists(mapping_file):
        return [], {}, []
    
    try:
        with open(mapping_file, 'r', encoding='utf-8') as f:
            class_mapping = json.load(f)
    except Exception as e:
        print(f"Error loading class mapping: {e}")
        return [], {}, []
    
    # 按类别名排序以确保一致性
    sorted_items = sorted(class_mapping.items(), key=lambda x: x[1])
    class_names = [item[0] for item in sorted_items]
    class2id = {name: id for name, id in sorted_items}
    class_ids = [id for _, id in sorted_items]
    
    # 如果无法从文件加载，则使用默认值
    if not class_names:
        try:
            from configs._base_.dataset.powscan import CLASS_NAMES, CLASS2ID, CLASS_IDS
            class_names = CLASS_NAMES
            class2id = CLASS2ID
            class_ids = CLASS_IDS
        except ImportError:
            pass
    
    return class_names, class2id, class_ids

def update_constants(data_root="data/powscan"):
    """
    更新全局常量
    
    Args:
        data_root: 数据集根目录
    """
    global CLASS_NAMES, CLASS2ID, CLASS_IDS
    
    CLASS_NAMES, CLASS2ID, CLASS_IDS = load_class_mapping(data_root)
    
    return CLASS_NAMES, CLASS2ID, CLASS_IDS