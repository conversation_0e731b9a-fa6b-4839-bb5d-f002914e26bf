from pathlib import Path


CLASS_LABELS_40 = (
    "wall",  # 0
    "floor",  # 1
    "chair",  # 2
    "door",  # 3
    "table",  # 4
    "picture",  # 5
    "cabinet",  # 6
    "cushion",  # 7
    "window",  # 8
    "sofa",  # 9
    "bed",  # 10
    "curtain",  # 11
    "chest_of_drawers",  # 12
    "plant",  # 13
    "sink",  # 14
    "stairs",  # 15
    "ceiling",  # 16
    "toilet",  # 17
    "stool",  # 18
    "towel",  # 19
    "mirror",  # 20
    "tv_monitor",  # 21
    "shower",  # 22
    "column",  # 23
    "bathtub",  # 24
    "counter",  # 25
    "fireplace",  # 26
    "lighting",  # 27
    "beam",  # 28
    "railing",  # 29
    "shelving",  # 30
    "blinds",  # 31
    "gym_equipment",  # 32
    "seating",  # 33
    "board_panel",  # 34
    "furniture",  # 35
    "appliances",  # 36
    "clothes",  # 37
    "objects",  # 38
    "misc",  # 39
)
