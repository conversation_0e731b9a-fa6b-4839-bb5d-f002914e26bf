#!/usr/bin/env python3
"""
Debug script to identify the PowScan training issue
"""

import os
import sys
import numpy as np
import traceback

# Add the project root to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

from pointcept.datasets.powscan import PowScanDataset

def test_dataset_loading():
    """Test dataset loading to identify problematic samples"""
    print("Testing PowScan dataset loading...")
    
    # Initialize the dataset
    dataset = PowScanDataset(
        split="train",
        data_root="data/powscan",
        test_mode=False
    )
    
    print(f"Dataset size: {len(dataset.data_list)}")
    print(f"Total samples (with loop): {len(dataset)}")
    
    # Test loading samples around where training failed
    # Training failed at iteration 3312 in epoch 5
    # With batch_size=12 and 2 GPUs, that's batch_size_per_gpu=6
    # So iteration 3312 corresponds to sample index around 3312 * 6 = 19872
    
    problematic_indices = []
    
    # Test a range of samples around the failure point
    start_idx = 19800
    end_idx = min(19900, len(dataset))
    
    print(f"\nTesting samples from {start_idx} to {end_idx}...")
    
    for i in range(start_idx, end_idx):
        try:
            data = dataset.get_data(i)
            
            # Check for empty arrays
            if data['coord'].shape[0] == 0:
                print(f"Sample {i}: EMPTY COORDINATE ARRAY")
                problematic_indices.append(i)
                continue
                
            # Check for invalid coordinates
            if np.any(np.isnan(data['coord'])) or np.any(np.isinf(data['coord'])):
                print(f"Sample {i}: INVALID COORDINATES (NaN/Inf)")
                problematic_indices.append(i)
                continue
                
            # Check for invalid segments
            if 'segment' in data and (np.any(np.isnan(data['segment'])) or np.any(np.isinf(data['segment']))):
                print(f"Sample {i}: INVALID SEGMENTS (NaN/Inf)")
                problematic_indices.append(i)
                continue
                
            # Check for extremely large coordinates that might cause memory issues
            coord_range = np.max(data['coord'], axis=0) - np.min(data['coord'], axis=0)
            if np.any(coord_range > 1e6):
                print(f"Sample {i}: EXTREMELY LARGE COORDINATE RANGE: {coord_range}")
                problematic_indices.append(i)
                continue
                
            if i % 10 == 0:
                print(f"Sample {i}: OK - coord shape: {data['coord'].shape}")
                
        except Exception as e:
            print(f"Sample {i}: ERROR - {e}")
            print(f"  Data path: {dataset.data_list[i % len(dataset.data_list)]}")
            traceback.print_exc()
            problematic_indices.append(i)
    
    print(f"\nFound {len(problematic_indices)} problematic samples: {problematic_indices}")
    return problematic_indices

def test_grid_splitting():
    """Test the grid splitting logic specifically"""
    print("\nTesting grid splitting logic...")
    
    # Load a large file that would be split
    large_files = [
        "data/powscan/processed/数据9/17-18(17_18).npy",
        "data/powscan/processed/数据9/36-37(36_37).npy"
    ]
    
    for file_path in large_files:
        if os.path.exists(file_path):
            print(f"\nTesting file: {file_path}")
            try:
                data = np.load(file_path)
                print(f"Original data shape: {data.shape}")
                
                # Simulate the dataset's grid splitting logic
                dataset = PowScanDataset(split="train", data_root="data/powscan")
                
                # Create a data dict like the dataset would
                data_dict = {
                    "coord": data[:, :3].astype(np.float32),
                    "strength": data[:, 3:4].astype(np.float32),
                    "color": data[:, 4:7].astype(np.float32),
                    "segment": data[:, 7].astype(np.int32),
                    "name": os.path.basename(file_path)
                }
                
                # Test grid splitting
                if data_dict["coord"].shape[0] > dataset.MAX_POINTS_PER_SAMPLE:
                    print(f"File needs splitting (>{dataset.MAX_POINTS_PER_SAMPLE} points)")
                    segments = dataset._split_by_coordinate_grid(data_dict)
                    print(f"Split into {len(segments)} segments")
                    
                    for i, segment in enumerate(segments):
                        if segment["coord"].shape[0] == 0:
                            print(f"  Segment {i}: EMPTY!")
                        else:
                            print(f"  Segment {i}: {segment['coord'].shape[0]} points")
                            
            except Exception as e:
                print(f"Error processing {file_path}: {e}")
                traceback.print_exc()

def main():
    """Main debug function"""
    print("Running PowScan training issue debug...")
    
    # Test dataset loading
    problematic_indices = test_dataset_loading()
    
    # Test grid splitting
    test_grid_splitting()
    
    if problematic_indices:
        print(f"\nFound issues with samples: {problematic_indices}")
        print("This could be causing the training to fail.")
    else:
        print("\nNo obvious issues found with dataset loading.")
        print("The issue might be related to memory, GPU, or other system resources.")

if __name__ == "__main__":
    main()
