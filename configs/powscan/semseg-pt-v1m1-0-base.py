"""
PowScan Base Configuration

Author: Your Name
Please cite our work if the code is helpful to you.
"""

from .._base_.default_runtime import *
from .._base_.dataset.powscan import *
import numpy as np

# 模型设置
model = dict(
    type="DefaultSegmentor",
    backbone=dict(
        type="PointTransformer-S",
        in_channels=6,  # XYZ + RGB
        num_classes=len(data.names)
    ),
    criteria=[
        dict(type="CrossEntropyLoss", loss_weight=1.0, ignore_index=-1)
    ]
)

# 数据集设置
dataset_type = "PowScanDataset"
data_root = "data/powscan"

train_pipeline = [
    dict(type="LoadPoints", backend_args=dict(load_dim=6, use_dim=6)),
    dict(type="LoadSegLabels"),
    dict(type="NormalizePoints"),
    dict(type="RandomRotate", angle=[0, 2*np.pi], axis=2, p=0.5),
    dict(type="RandomRotate", angle=[0, 0.1*np.pi], axis=0, p=0.3),
    dict(type="RandomRotate", angle=[0, 0.1*np.pi], axis=1, p=0.3),
    dict(type="RandomScale", scale=[0.9, 1.1]),
    dict(type="RandomShift", shift=[0.2, 0.2, 0.2]),
    dict(type="RandomFlip", p=0.5),
    dict(type="RandomJitter", sigma=0.005, clip=0.02),
    dict(type="GridSample", size=10000, mode="train"),
    dict(type="ToTensor"),
    dict(
        type="Collect",
        keys=("coord", "segment"),
        feat_keys=("coord", "normal"),
    ),
]

test_pipeline = [
    dict(type="LoadPoints", backend_args=dict(load_dim=6, use_dim=6)),
    dict(type="LoadSegLabels"),
    dict(type="NormalizePoints"),
    dict(
        type="Collect",
        keys=("coord", "segment"),
        feat_keys=("coord", "normal"),
        no_augmentation=True,
    ),
]

# 运行时设置
train = dict(
    type=dataset_type,
    split="train",
    data_root=data_root,
    transform=train_pipeline,
    test_mode=False,
)

test = dict(
    type=dataset_type,
    split="val",  # 或 "test"
    data_root=data_root,
    transform=test_pipeline,
    test_mode=True,
    test_cfg=dict(
        voxelize=dict(
            type="GridSample",
            size=0.05,  # 网格大小
            mode="test",
            return_hash=True,
            keys=("coord", "segment")
        ),
        crop=None,
        post_transform=[
            dict(type="ToTensor"),
            dict(
                type="Collect",
                keys=("coord", "segment"),
                feat_keys=("coord", "normal")
            ),
        ],
        aug_transform=[
            [dict(type="RandomScale", scale=[0.9, 0.9])],
            [dict(type="RandomScale", scale=[1.0, 1.0])],
            [dict(type="RandomScale", scale=[1.1, 1.1])],
        ]
    ),
)

# 优化器设置
optimizer = dict(type="AdamW", lr=0.001, weight_decay=0.01)
scheduler = dict(type="MultiStepLR", milestones=[0.6, 0.8], gamma=0.1)

# 训练控制设置
epochs = 100
step_per_epoch = 1000
val_freq = 5  # 每5个epoch验证一次