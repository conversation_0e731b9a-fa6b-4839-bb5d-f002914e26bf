class_names = [
    "wall",
    "ceiling",
    "floor",
    "table",
    "door",
    "ceiling lamp",
    "cabinet",
    "blinds",
    "curtain",
    "chair",
    "storage cabinet",
    "office chair",
    "bookshelf",
    "whiteboard",
    "window",
    "box",
    "window frame",
    "monitor",
    "shelf",
    "doorframe",
    "pipe",
    "heater",
    "kitchen cabinet",
    "sofa",
    "windowsill",
    "bed",
    "shower wall",
    "trash can",
    "book",
    "plant",
    "blanket",
    "tv",
    "computer tower",
    "kitchen counter",
    "refrigerator",
    "jacket",
    "electrical duct",
    "sink",
    "bag",
    "picture",
    "pillow",
    "towel",
    "suitcase",
    "backpack",
    "crate",
    "keyboard",
    "rack",
    "toilet",
    "paper",
    "printer",
    "poster",
    "painting",
    "microwave",
    "board",
    "shoes",
    "socket",
    "bottle",
    "bucket",
    "cushion",
    "basket",
    "shoe rack",
    "telephone",
    "file folder",
    "cloth",
    "blind rail",
    "laptop",
    "plant pot",
    "exhaust fan",
    "cup",
    "coat hanger",
    "light switch",
    "speaker",
    "table lamp",
    "air vent",
    "clothes hanger",
    "kettle",
    "smoke detector",
    "container",
    "power strip",
    "slippers",
    "paper bag",
    "mouse",
    "cutting board",
    "toilet paper",
    "paper towel",
    "pot",
    "clock",
    "pan",
    "tap",
    "jar",
    "soap dispenser",
    "binder",
    "bowl",
    "tissue box",
    "whiteboard eraser",
    "toilet brush",
    "spray bottle",
    "headphones",
    "stapler",
    "marker",
]

data = dict(
    names=class_names,
)
